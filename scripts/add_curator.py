#!/usr/bin/env python3
"""
Скрипт для добавления куратора в базу данных и связывания его с группами
"""
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import (
    init_database, UserRepository, CuratorRepository, 
    GroupRepository, SubjectRepository
)


async def show_existing_data():
    """Показать существующие данные в базе"""
    print("📊 Текущие данные в базе:")
    print("=" * 50)
    
    # Показываем пользователей
    users = await UserRepository.get_all()
    print(f"\n👥 Пользователи ({len(users)}):")
    for user in users:
        print(f"  ID: {user.id}, Telegram ID: {user.telegram_id}, Имя: {user.name}, Роль: {user.role}")
    
    # Показываем предметы
    subjects = await SubjectRepository.get_all()
    print(f"\n📚 Предметы ({len(subjects)}):")
    for subject in subjects:
        print(f"  ID: {subject.id}, Название: {subject.name}")
    
    # Показываем группы
    groups = await GroupRepository.get_all()
    print(f"\n👥 Группы ({len(groups)}):")
    for group in groups:
        subject_name = group.subject.name if group.subject else "Без предмета"
        print(f"  ID: {group.id}, Название: {group.name}, Предмет: {subject_name}")
    
    # Показываем кураторов
    curators = await CuratorRepository.get_all()
    print(f"\n🎓 Кураторы ({len(curators)}):")
    for curator in curators:
        user_name = curator.user.name if curator.user else "Неизвестно"
        subject_name = curator.subject.name if curator.subject else "Без предмета"
        groups_count = len(curator.groups) if curator.groups else 0
        print(f"  ID: {curator.id}, Пользователь: {user_name}, Предмет: {subject_name}, Групп: {groups_count}")
        if curator.groups:
            for group in curator.groups:
                print(f"    - {group.name}")


async def add_curator_interactive():
    """Интерактивное добавление куратора"""
    print("\n🎓 Добавление куратора")
    print("=" * 30)
    
    # Запрашиваем Telegram ID
    telegram_id = input("Введите ваш Telegram ID: ").strip()
    if not telegram_id.isdigit():
        print("❌ Telegram ID должен быть числом!")
        return
    
    telegram_id = int(telegram_id)
    
    # Запрашиваем имя
    name = input("Введите ваше имя: ").strip()
    if not name:
        print("❌ Имя не может быть пустым!")
        return
    
    try:
        # Проверяем, существует ли пользователь
        existing_user = await UserRepository.get_by_telegram_id(telegram_id)
        
        if existing_user:
            print(f"✅ Пользователь найден: {existing_user.name} (роль: {existing_user.role})")
            
            # Обновляем роль на curator, если нужно
            if existing_user.role != 'curator':
                await UserRepository.update_role(existing_user.id, 'curator')
                print("✅ Роль обновлена на 'curator'")
            
            user_id = existing_user.id
        else:
            # Создаем нового пользователя
            user = await UserRepository.create(telegram_id, name, 'curator')
            print(f"✅ Создан новый пользователь: {user.name}")
            user_id = user.id
        
        # Проверяем, есть ли уже профиль куратора
        existing_curator = await CuratorRepository.get_by_user_id(user_id)
        
        if existing_curator:
            print(f"✅ Профиль куратора уже существует (ID: {existing_curator.id})")
            curator = existing_curator
        else:
            # Создаем профиль куратора
            curator = await CuratorRepository.create(user_id)
            print(f"✅ Создан профиль куратора (ID: {curator.id})")
        
        # Показываем доступные группы
        groups = await GroupRepository.get_all()
        if not groups:
            print("❌ В базе нет групп для назначения")
            return
        
        print(f"\n📋 Доступные группы ({len(groups)}):")
        for i, group in enumerate(groups, 1):
            subject_name = group.subject.name if group.subject else "Без предмета"
            print(f"  {i}. {group.name} ({subject_name})")
        
        # Запрашиваем группы для назначения
        print("\nВведите номера групп для назначения (через запятую, например: 1,3,5):")
        print("Или введите 'all' для назначения всех групп:")
        group_input = input("Группы: ").strip()
        
        if group_input.lower() == 'all':
            selected_groups = groups
        else:
            try:
                group_numbers = [int(x.strip()) for x in group_input.split(',')]
                selected_groups = []
                for num in group_numbers:
                    if 1 <= num <= len(groups):
                        selected_groups.append(groups[num - 1])
                    else:
                        print(f"⚠️ Номер группы {num} вне диапазона")
            except ValueError:
                print("❌ Неверный формат номеров групп!")
                return
        
        if not selected_groups:
            print("❌ Не выбрано ни одной группы!")
            return
        
        # Назначаем группы куратору
        print(f"\n🔗 Назначение {len(selected_groups)} групп куратору...")
        for group in selected_groups:
            success = await CuratorRepository.add_curator_to_group(curator.id, group.id)
            if success:
                print(f"  ✅ Назначена группа: {group.name}")
            else:
                print(f"  ❌ Ошибка назначения группы: {group.name}")
        
        print(f"\n🎉 Куратор успешно настроен!")
        print(f"   Имя: {name}")
        print(f"   Telegram ID: {telegram_id}")
        print(f"   Назначено групп: {len(selected_groups)}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")


async def main():
    """Главная функция"""
    print("🎓 Управление кураторами")
    print("=" * 40)
    
    # Инициализируем базу данных
    await init_database()
    
    while True:
        print("\nВыберите действие:")
        print("1. Показать текущие данные")
        print("2. Добавить куратора")
        print("3. Выход")
        
        choice = input("\nВаш выбор (1-3): ").strip()
        
        if choice == '1':
            await show_existing_data()
        elif choice == '2':
            await add_curator_interactive()
        elif choice == '3':
            print("👋 До свидания!")
            break
        else:
            print("❌ Неверный выбор!")


if __name__ == "__main__":
    asyncio.run(main())
