#!/usr/bin/env python3
"""
Тестовый скрипт для проверки функции самоназначения ролей администратором
"""
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import init_database
from admin.utils.common import check_existing_user_for_role_assignment


async def test_admin_self_assignment():
    """Тестирование функции самоназначения ролей"""
    print("🧪 Тестирование функции самоназначения ролей администратором")
    print("=" * 60)
    
    # Инициализируем базу данных
    await init_database()
    
    # Тестовые данные
    admin_telegram_id = 955518340  # Ваш Telegram ID (админ)
    other_user_telegram_id = 111222333  # Другой пользователь
    non_existing_telegram_id = 999999999999  # Несуществующий пользователь
    
    print(f"🔍 Тест 1: Админ добавляет себя как куратора")
    result1 = await check_existing_user_for_role_assignment(
        admin_telegram_id, 'curator', admin_telegram_id
    )
    print(f"   Результат: can_assign={result1['can_assign']}")
    print(f"   Сообщение: {result1['message']}")
    print()
    
    print(f"🔍 Тест 2: Админ пытается добавить другого существующего пользователя")
    result2 = await check_existing_user_for_role_assignment(
        other_user_telegram_id, 'curator', admin_telegram_id
    )
    print(f"   Результат: can_assign={result2['can_assign']}")
    print(f"   Сообщение: {result2['message']}")
    print()
    
    print(f"🔍 Тест 3: Админ добавляет несуществующего пользователя")
    result3 = await check_existing_user_for_role_assignment(
        non_existing_telegram_id, 'curator', admin_telegram_id
    )
    print(f"   Результат: can_assign={result3['can_assign']}")
    print(f"   Сообщение: {result3['message']}")
    print()
    
    print(f"🔍 Тест 4: Обычный пользователь пытается добавить себя")
    result4 = await check_existing_user_for_role_assignment(
        other_user_telegram_id, 'curator', other_user_telegram_id
    )
    print(f"   Результат: can_assign={result4['can_assign']}")
    print(f"   Сообщение: {result4['message']}")
    print()
    
    # Резюме
    print("📊 Резюме тестов:")
    print(f"   ✅ Админ может добавить себя: {result1['can_assign']}")
    print(f"   ❌ Админ НЕ может добавить другого существующего: {not result2['can_assign']}")
    print(f"   ✅ Админ может добавить нового пользователя: {result3['can_assign']}")
    print(f"   ❌ Обычный пользователь НЕ может добавить себя: {not result4['can_assign']}")
    
    all_tests_passed = (
        result1['can_assign'] and 
        not result2['can_assign'] and 
        result3['can_assign'] and 
        not result4['can_assign']
    )
    
    if all_tests_passed:
        print("\n🎉 Все тесты прошли успешно!")
    else:
        print("\n❌ Некоторые тесты не прошли!")


if __name__ == "__main__":
    asyncio.run(test_admin_self_assignment())
