#!/usr/bin/env python3
"""
Скрипт для запуска бота с ngrok в режиме разработки
Работает на Windows, Linux и macOS
"""
import os
import sys
import time
import json
import signal
import subprocess
import requests
from pathlib import Path

def check_ngrok():
    """Проверить установку ngrok"""
    try:
        subprocess.run(["ngrok", "version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def check_bot_token():
    """Проверить наличие BOT_TOKEN"""
    token = os.getenv("BOT_TOKEN")
    if not token:
        # Попробовать загрузить из .env
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file) as f:
                for line in f:
                    if line.startswith("BOT_TOKEN="):
                        token = line.split("=", 1)[1].strip()
                        os.environ["BOT_TOKEN"] = token
                        break
    return token

def start_redis():
    """Запустить Redis контейнер если не запущен"""
    try:
        result = subprocess.run(["docker", "ps"], capture_output=True, text=True)
        if "redis" not in result.stdout:
            print("🐳 Запуск Redis контейнера...")
            subprocess.run([
                "docker", "run", "-d", "--name", "redis-local", 
                "-p", "6379:6379", "redis:alpine", 
                "redis-server", "--requirepass", "q3terhWD23WWDwdfsdt"
            ], check=True)
            time.sleep(3)
            print("✅ Redis запущен")
        else:
            print("✅ Redis уже запущен")
    except subprocess.CalledProcessError as e:
        print(f"⚠️ Не удалось запустить Redis: {e}")

def start_ngrok():
    """Запустить ngrok и получить URL"""
    print("🌐 Запуск ngrok туннеля на порт 8000...")
    
    # Запускаем ngrok в фоне
    ngrok_process = subprocess.Popen(
        ["ngrok", "http", "8000", "--log=stdout"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    # Ждем запуска
    time.sleep(5)
    
    # Получаем URL через API
    try:
        response = requests.get("http://localhost:4040/api/tunnels", timeout=10)
        data = response.json()
        
        if data.get("tunnels"):
            tunnel = data["tunnels"][0]
            ngrok_url = tunnel["public_url"]
            print(f"✅ ngrok URL: {ngrok_url}")
            return ngrok_process, ngrok_url
        else:
            raise Exception("Нет активных туннелей")
            
    except Exception as e:
        print(f"❌ Не удалось получить ngrok URL: {e}")
        ngrok_process.terminate()
        return None, None

def set_webhook(bot_token, ngrok_url):
    """Установить webhook"""
    webhook_url = f"{ngrok_url}/webhook"
    print(f"🔗 Установка webhook: {webhook_url}")
    
    try:
        response = requests.post(
            f"https://api.telegram.org/bot{bot_token}/setWebhook",
            data={"url": webhook_url},
            timeout=10
        )
        result = response.json()
        
        if result.get("ok"):
            print("✅ Webhook установлен успешно!")
            return True
        else:
            print(f"❌ Ошибка установки webhook: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при установке webhook: {e}")
        return False

def delete_webhook(bot_token):
    """Удалить webhook"""
    try:
        response = requests.post(
            f"https://api.telegram.org/bot{bot_token}/deleteWebhook",
            timeout=10
        )
        print("🔗 Webhook удален")
    except Exception as e:
        print(f"⚠️ Ошибка при удалении webhook: {e}")

def create_env_file(bot_token, ngrok_url):
    """Создать временный .env файл"""
    env_content = f"""BOT_TOKEN={bot_token}
WEBHOOK_MODE=true
WEBHOOK_HOST={ngrok_url}
WEBHOOK_PATH=/webhook
WEB_SERVER_HOST=0.0.0.0
WEB_SERVER_PORT=8000
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=q3terhWD23WWDwdfsdt
"""
    
    with open(".env.ngrok", "w") as f:
        f.write(env_content)
    print("📝 Временная конфигурация создана")

def cleanup(bot_token, ngrok_process):
    """Очистка при завершении"""
    print("\n🛑 Остановка...")
    
    if bot_token:
        delete_webhook(bot_token)
    
    if ngrok_process:
        print("🌐 Остановка ngrok...")
        ngrok_process.terminate()
        ngrok_process.wait()
    
    if os.path.exists(".env.ngrok"):
        os.remove(".env.ngrok")
        print("📝 Временная конфигурация удалена")
    
    print("✅ Очистка завершена")

def main():
    """Главная функция"""
    print("🚀 Запуск бота в режиме разработки с ngrok...")
    
    # Проверки
    if not check_ngrok():
        print("❌ ngrok не установлен. Установите его сначала.")
        return 1
    
    bot_token = check_bot_token()
    if not bot_token:
        print("❌ BOT_TOKEN не найден. Создайте .env файл или экспортируйте переменную")
        return 1
    
    ngrok_process = None
    
    try:
        # Запуск Redis
        start_redis()
        
        # Запуск ngrok
        ngrok_process, ngrok_url = start_ngrok()
        if not ngrok_url:
            return 1
        
        # Установка webhook
        if not set_webhook(bot_token, ngrok_url):
            return 1
        
        # Создание конфигурации
        create_env_file(bot_token, ngrok_url)
        
        print("🎉 Запуск бота в webhook режиме...")
        print()
        print("📱 Напишите боту /start в Telegram")
        print("🌐 ngrok интерфейс: http://localhost:4040")
        print(f"📊 Статистика: {ngrok_url}/stats")
        print(f"💚 Здоровье: {ngrok_url}/health")
        print()
        print("⚠️  Для остановки нажмите Ctrl+C")
        print()
        
        # Настройка обработчика сигналов
        def signal_handler(sig, frame):
            cleanup(bot_token, ngrok_process)
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        # Загрузка переменных из временного .env
        with open(".env.ngrok") as f:
            for line in f:
                if "=" in line:
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value
        
        # Запуск бота
        subprocess.run([sys.executable, "main.py"])
        
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return 1
    finally:
        cleanup(bot_token, ngrok_process)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
