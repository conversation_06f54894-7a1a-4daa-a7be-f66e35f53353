#!/usr/bin/env python3
"""
Тестовый скрипт для проверки исправленной функции add_curator
"""
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from admin.utils.common import add_curator
from database.repositories.curator_repository import CuratorRepository
from database.repositories.user_repository import UserRepository


async def test_add_curator_fix():
    """Тестирование исправленной функции add_curator"""
    print("\n🧪 Тестирование исправленной функции add_curator")
    print("=" * 60)
    
    await init_database()
    
    # Тестовые данные
    name = "Андрей Климов"
    telegram_id = 955518340
    course_id = 1  # Предполагаем, что курс с ID 1 существует
    subject_id = 1  # Предполагаем, что предмет с ID 1 существует
    group_id = 1   # Предполагаем, что группа с ID 1 существует
    
    print(f"📋 Тестовые данные:")
    print(f"   Имя: {name}")
    print(f"   Telegram ID: {telegram_id}")
    print(f"   Course ID: {course_id}")
    print(f"   Subject ID: {subject_id}")
    print(f"   Group ID: {group_id}")
    
    # Проверяем существующего пользователя
    existing_user = await UserRepository.get_by_telegram_id(telegram_id)
    if existing_user:
        print(f"👤 Найден существующий пользователь: {existing_user.name} (роль: {existing_user.role})")
    else:
        print(f"❌ Пользователь с Telegram ID {telegram_id} не найден")
        return
    
    # Проверяем, есть ли уже профиль куратора
    existing_curator = await CuratorRepository.get_by_user_id(existing_user.id)
    if existing_curator:
        print(f"⚠️ У пользователя уже есть профиль куратора (ID: {existing_curator.id})")
        print("🗑️ Удаляем существующий профиль куратора для чистого теста...")
        await CuratorRepository.delete(existing_curator.id)
    
    # Тестируем функцию add_curator
    print(f"\n🚀 Вызываем add_curator...")
    success = await add_curator(name, telegram_id, course_id, subject_id, group_id)
    
    if success:
        print(f"✅ Функция add_curator вернула True")
        
        # Проверяем, создался ли профиль куратора
        new_curator = await CuratorRepository.get_by_user_id(existing_user.id)
        if new_curator:
            print(f"✅ Профиль куратора создан (ID: {new_curator.id})")
            print(f"   User ID: {new_curator.user_id}")
            print(f"   Course ID: {new_curator.course_id}")
            print(f"   Subject ID: {new_curator.subject_id}")
            
            # Проверяем группы куратора
            curator_groups = await CuratorRepository.get_curator_groups(new_curator.id)
            if curator_groups:
                print(f"✅ Куратор добавлен в группы:")
                for group in curator_groups:
                    print(f"   - Группа ID: {group.id}, Название: {group.name}")
            else:
                print(f"❌ Куратор не добавлен ни в одну группу")
        else:
            print(f"❌ Профиль куратора НЕ создан!")
    else:
        print(f"❌ Функция add_curator вернула False")


if __name__ == "__main__":
    asyncio.run(test_add_curator_fix())
