#!/usr/bin/env python3
"""
Проверка записей в таблице curators
"""
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from database.repositories.curator_repository import CuratorRepository
from database.repositories.user_repository import UserRepository


async def check_curator_records():
    """Проверить записи в таблице curators"""
    print("\n🔍 Проверка записей в таблице curators")
    print("=" * 50)
    
    await init_database()
    
    # Получаем всех кураторов
    curators = await CuratorRepository.get_all()
    
    if curators:
        print(f"📊 Найдено кураторов: {len(curators)}")
        for curator in curators:
            print(f"\n👤 Куратор ID: {curator.id}")
            print(f"   User ID: {curator.user_id}")
            print(f"   Имя пользователя: {curator.user.name}")
            print(f"   Роль пользователя: {curator.user.role}")
            print(f"   Telegram ID: {curator.user.telegram_id}")
            print(f"   Course ID: {curator.course_id}")
            print(f"   Subject ID: {curator.subject_id}")
            
            # Проверяем группы куратора
            groups = await CuratorRepository.get_curator_groups(curator.id)
            if groups:
                print(f"   Группы:")
                for group in groups:
                    print(f"     - {group.name} (ID: {group.id})")
            else:
                print(f"   Группы: нет")
    else:
        print("❌ Кураторы не найдены")
    
    # Проверяем конкретно админа
    print(f"\n🔍 Проверка админа (Telegram ID: 955518340)")
    admin_user = await UserRepository.get_by_telegram_id(955518340)
    if admin_user:
        print(f"👤 Админ найден: {admin_user.name} (ID: {admin_user.id}, роль: {admin_user.role})")
        
        admin_curator = await CuratorRepository.get_by_user_id(admin_user.id)
        if admin_curator:
            print(f"✅ У админа есть профиль куратора (ID: {admin_curator.id})")
        else:
            print(f"❌ У админа НЕТ профиля куратора")
    else:
        print(f"❌ Админ не найден")


if __name__ == "__main__":
    asyncio.run(check_curator_records())
