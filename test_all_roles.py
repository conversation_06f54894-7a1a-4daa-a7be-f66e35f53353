#!/usr/bin/env python3
"""
Тестовый скрипт для проверки всех исправленных функций добавления ролей
"""
import asyncio
import sys
import os

# Добавляем путь к корневой папке проекта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import init_database
from admin.utils.common import add_student, add_teacher, add_manager
from database.repositories.student_repository import StudentRepository
from database.repositories.teacher_repository import TeacherRepository
from database.repositories.manager_repository import ManagerRepository
from database.repositories.user_repository import UserRepository


async def test_all_role_functions():
    """Тестирование всех исправленных функций добавления ролей"""
    print("\n🧪 Тестирование всех функций добавления ролей")
    print("=" * 60)
    
    await init_database()
    
    # Тестовые данные админа
    admin_name = "Андрей Климов"
    admin_telegram_id = 955518340
    
    # Проверяем существующего админа
    admin_user = await UserRepository.get_by_telegram_id(admin_telegram_id)
    if not admin_user:
        print(f"❌ Админ с Telegram ID {admin_telegram_id} не найден")
        return
    
    print(f"👤 Найден админ: {admin_user.name} (роль: {admin_user.role})")
    
    # Тест 1: Добавление админа как студента
    print(f"\n🎓 Тест 1: Добавление админа как студента")
    print("-" * 40)
    
    # Удаляем существующий профиль студента, если есть
    existing_student = await StudentRepository.get_by_user_id(admin_user.id)
    if existing_student:
        print(f"🗑️ Удаляем существующий профиль студента...")
        await StudentRepository.delete(existing_student.id)
    
    success = await add_student(admin_name, admin_telegram_id, 1, "premium")
    if success:
        print(f"✅ Админ успешно добавлен как студент")
        student = await StudentRepository.get_by_user_id(admin_user.id)
        if student:
            print(f"   Student ID: {student.id}, Тариф: {student.tariff}")
    else:
        print(f"❌ Ошибка при добавлении админа как студента")
    
    # Тест 2: Добавление админа как преподавателя
    print(f"\n👨‍🏫 Тест 2: Добавление админа как преподавателя")
    print("-" * 40)
    
    # Удаляем существующий профиль преподавателя, если есть
    existing_teacher = await TeacherRepository.get_by_user_id(admin_user.id)
    if existing_teacher:
        print(f"🗑️ Удаляем существующий профиль преподавателя...")
        await TeacherRepository.delete(existing_teacher.id)
    
    success = await add_teacher(admin_name, admin_telegram_id, 1, 1, 1)
    if success:
        print(f"✅ Админ успешно добавлен как преподаватель")
        teacher = await TeacherRepository.get_by_user_id(admin_user.id)
        if teacher:
            print(f"   Teacher ID: {teacher.id}")
    else:
        print(f"❌ Ошибка при добавлении админа как преподавателя")
    
    # Тест 3: Добавление админа как менеджера
    print(f"\n👔 Тест 3: Добавление админа как менеджера")
    print("-" * 40)
    
    # Удаляем существующий профиль менеджера, если есть
    existing_manager = await ManagerRepository.get_by_user_id(admin_user.id)
    if existing_manager:
        print(f"🗑️ Удаляем существующий профиль менеджера...")
        await ManagerRepository.delete(existing_manager.id)
    
    success = await add_manager(admin_name, admin_telegram_id)
    if success:
        print(f"✅ Админ успешно добавлен как менеджер")
        manager = await ManagerRepository.get_by_user_id(admin_user.id)
        if manager:
            print(f"   Manager ID: {manager.id}")
    else:
        print(f"❌ Ошибка при добавлении админа как менеджера")
    
    print(f"\n🎉 Все тесты завершены!")


if __name__ == "__main__":
    asyncio.run(test_all_role_functions())
